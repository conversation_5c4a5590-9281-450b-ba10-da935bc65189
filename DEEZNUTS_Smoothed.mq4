//+------------------------------------------------------------------+
//|                                            DEEZNUTS_Smoothed.mq4 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   1

#property indicator_label1  "DEEZNUTS"
#property indicator_type1   DRAW_COLOR_HISTOGRAM
#property indicator_color1  clrBlue,clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3

// Input parameters
input int    FastLength = 1;           // Fast MA length
input int    SlowLengthIn = 5;         // Slow MA length  
input int    SmoothLength = 3;         // Oscillator smoothing
input string MAType = "EMA";           // MA type: SMA, EMA, RMA, WMA

// Indicator buffers
double HistogramBuffer[];
double ColorBuffer[];
double FastMABuffer[];
double SlowMABuffer[];

// Global variables
int SlowLength;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Make sure SlowLength > FastLength
    SlowLength = MathMax(SlowLengthIn, FastLength + 1);
    
    // Set indicator buffers
    SetIndexBuffer(0, HistogramBuffer);
    SetIndexBuffer(1, ColorBuffer);
    SetIndexBuffer(2, FastMABuffer);
    SetIndexBuffer(3, SlowMABuffer);
    
    // Set drawing settings
    SetIndexStyle(0, DRAW_COLOR_HISTOGRAM, STYLE_SOLID, 3);
    SetIndexLabel(0, "DEEZNUTS Histogram");
    
    // Set indicator name
    string short_name = StringFormat("DEEZNUTS-S(%d,%d,%d,%s)", 
                                    FastLength, SlowLength, SmoothLength, MAType);
    IndicatorShortName(short_name);
    
    // Set precision
    IndicatorDigits(Digits + 1);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Moving Average calculation function                              |
//+------------------------------------------------------------------+
double CalculateMA(int index, const double& price[], int period, string ma_type)
{
    double result = 0.0;
    
    if(ma_type == "SMA")
    {
        result = iMA(NULL, 0, period, 0, MODE_SMA, PRICE_CLOSE, index);
    }
    else if(ma_type == "EMA")
    {
        result = iMA(NULL, 0, period, 0, MODE_EMA, PRICE_CLOSE, index);
    }
    else if(ma_type == "RMA" || ma_type == "SMMA")
    {
        result = iMA(NULL, 0, period, 0, MODE_SMMA, PRICE_CLOSE, index);
    }
    else if(ma_type == "WMA")
    {
        result = iMA(NULL, 0, period, 0, MODE_LWMA, PRICE_CLOSE, index);
    }
    else
    {
        result = iMA(NULL, 0, period, 0, MODE_SMA, PRICE_CLOSE, index);
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| RMA (Running Moving Average) calculation                         |
//+------------------------------------------------------------------+
double CalculateRMA(int index, const double& source[], int period)
{
    static double rma_buffer[];
    
    if(ArraySize(rma_buffer) != Bars)
        ArrayResize(rma_buffer, Bars);
    
    if(index >= Bars - period)
    {
        // Initial SMA calculation for the first value
        double sum = 0.0;
        for(int i = 0; i < period; i++)
        {
            sum += source[index + i];
        }
        rma_buffer[index] = sum / period;
    }
    else
    {
        // RMA calculation: (previous_rma * (period - 1) + current_value) / period
        double alpha = 1.0 / period;
        rma_buffer[index] = alpha * source[index] + (1.0 - alpha) * rma_buffer[index + 1];
    }
    
    return rma_buffer[index];
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    int limit;
    
    if(prev_calculated == 0)
        limit = rates_total - 1;
    else
        limit = rates_total - prev_calculated;
    
    // Calculate from oldest to newest
    for(int i = limit; i >= 0; i--)
    {
        // Calculate Fast and Slow MAs
        FastMABuffer[i] = CalculateMA(i, close, FastLength, MAType);
        SlowMABuffer[i] = CalculateMA(i, close, SlowLength, MAType);
        
        // Calculate raw oscillator
        double raw = FastMABuffer[i] - SlowMABuffer[i];
        
        // Apply smoothing if needed
        double osc;
        if(SmoothLength > 1)
        {
            // Create temporary array for RMA calculation
            static double raw_buffer[];
            if(ArraySize(raw_buffer) != rates_total)
                ArrayResize(raw_buffer, rates_total);
            raw_buffer[i] = raw;
            
            osc = CalculateRMA(i, raw_buffer, SmoothLength);
        }
        else
        {
            osc = raw;
        }
        
        // Set histogram value (scaled for visibility)
        HistogramBuffer[i] = osc * 10000; // Scale factor for visibility
        
        // Set color based on oscillator value
        if(osc >= 0)
            ColorBuffer[i] = 0; // Blue (first color)
        else
            ColorBuffer[i] = 1; // Red (second color)
    }
    
    return(rates_total);
}
